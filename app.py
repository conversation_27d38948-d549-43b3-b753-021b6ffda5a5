from flask import Flask, request, jsonify, render_template_string, send_from_directory
from flask_cors import CORS
import requests
from bs4 import BeautifulSoup
import re
import time
import random
from urllib.parse import urljoin, urlparse
import logging

app = Flask(__name__)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MovieScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def scrape_mycima(self, url):
        """Scrape movies/series from my-cima.net"""
        try:
            logger.info(f"Scraping URL: {url}")
            
            # Add random delay to avoid being blocked
            time.sleep(random.uniform(1, 3))
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Try different selectors for movie cards
            movie_cards = []
            
            # Common selectors for my-cima.net
            selectors = [
                '.Grid--WecimaPosts .GridItem',
                '.movies-list .movie-item',
                '.Grid .GridItem',
                '.post-item',
                '.movie-card',
                '.film-item'
            ]
            
            for selector in selectors:
                cards = soup.select(selector)
                if cards:
                    movie_cards = cards
                    logger.info(f"Found {len(cards)} items using selector: {selector}")
                    break
            
            if not movie_cards:
                # Fallback: look for any div with movie-related classes
                movie_cards = soup.find_all('div', class_=re.compile(r'(movie|film|post|item)', re.I))
                logger.info(f"Fallback: Found {len(movie_cards)} items")
            
            movies_data = []
            
            for card in movie_cards[:50]:  # Limit to first 50 items
                try:
                    movie_info = self.extract_movie_info(card, url)
                    if movie_info:
                        movies_data.append(movie_info)
                except Exception as e:
                    logger.warning(f"Error extracting movie info: {e}")
                    continue
            
            # Determine if it's movies or series based on URL
            is_series = any(keyword in url.lower() for keyword in ['series', 'مسلسل', 'مسلسلات', 'tv'])
            
            if is_series:
                return {
                    "series_info": [
                        {
                            "series_name": item["movies_name"],
                            "series_img": item["movies_img"],
                            "series_href": item["movies_href"]
                        } for item in movies_data
                    ]
                }
            else:
                return {"movies_info": movies_data}
                
        except requests.RequestException as e:
            logger.error(f"Request error: {e}")
            raise Exception(f"خطأ في الاتصال بالموقع: {str(e)}")
        except Exception as e:
            logger.error(f"Scraping error: {e}")
            raise Exception(f"خطأ في استخراج البيانات: {str(e)}")

    def extract_movie_info(self, card, base_url):
        """Extract movie information from a card element"""
        movie_info = {
            "movies_name": "غير محدد",
            "movies_img": "",
            "movies_href": ""
        }
        
        # Extract title
        title_selectors = [
            '.title a',
            '.post-title a',
            '.movie-title a',
            'h2 a',
            'h3 a',
            '.GridItem--Title a',
            'a[title]'
        ]
        
        title_element = None
        for selector in title_selectors:
            title_element = card.select_one(selector)
            if title_element:
                break
        
        if title_element:
            movie_info["movies_name"] = title_element.get_text(strip=True)
            href = title_element.get('href', '')
            if href:
                movie_info["movies_href"] = urljoin(base_url, href)
        
        # Extract image
        img_selectors = [
            'img',
            '.poster img',
            '.movie-poster img',
            '.GridItem--Image img'
        ]
        
        img_element = None
        for selector in img_selectors:
            img_element = card.select_one(selector)
            if img_element:
                break
        
        if img_element:
            img_src = img_element.get('src') or img_element.get('data-src') or img_element.get('data-lazy-src')
            if img_src:
                movie_info["movies_img"] = urljoin(base_url, img_src)
        
        # Only return if we have at least a title
        if movie_info["movies_name"] != "غير محدد":
            return movie_info
        
        return None

@app.route('/')
def index():
    """Serve the main HTML page"""
    try:
        with open('index.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "HTML file not found", 404

@app.route('/script.js')
def script():
    """Serve the JavaScript file"""
    try:
        with open('script.js', 'r', encoding='utf-8') as f:
            content = f.read()
            return content, 200, {'Content-Type': 'application/javascript; charset=utf-8'}
    except FileNotFoundError:
        return "JavaScript file not found", 404

@app.route('/scrape', methods=['POST'])
def scrape():
    """Handle scraping requests"""
    try:
        data = request.get_json()
        url = data.get('url')
        
        if not url:
            return jsonify({"error": "يرجى تقديم رابط صحيح"}), 400
        
        # Validate URL
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return jsonify({"error": "رابط غير صحيح"}), 400
        
        scraper = MovieScraper()
        
        # Check if it's a supported site
        if 'cima' in parsed_url.netloc.lower():
            result = scraper.scrape_mycima(url)
        else:
            # Try generic scraping
            result = scraper.scrape_mycima(url)
        
        if not result or (not result.get('movies_info') and not result.get('series_info')):
            return jsonify({"error": "لم يتم العثور على أي بيانات في هذا الرابط"}), 404
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Scraping error: {e}")
        return jsonify({"error": str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "الصفحة غير موجودة"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "خطأ داخلي في الخادم"}), 500

if __name__ == '__main__':
    print("🎬 تطبيق مستخرج بيانات الأفلام والمسلسلات")
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("📱 افتح المتصفح وانتقل إلى الرابط أعلاه")
    app.run(debug=True, host='0.0.0.0', port=5000)
