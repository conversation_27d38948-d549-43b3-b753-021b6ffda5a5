# مستخرج بيانات الأفلام والمسلسلات 🎬

تطبيق ويب لاستخراج بيانات الأفلام والمسلسلات من مواقع مثل my-cima.net مع إمكانية التصدير بصيغة JSON.

## المميزات ✨

- واجهة مستخدم بسيطة وجذابة باللغة العربية
- استخراج بيانات الأفلام والمسلسلات (الاسم، الصورة، الرابط)
- دعم مواقع متعددة مثل my-cima.net
- تصدير البيانات بصيغة JSON
- عرض النتائج في شكل بطاقات منظمة
- تصميم متجاوب يعمل على جميع الأجهزة

## التثبيت والتشغيل 🚀

### المتطلبات
- Python 3.7 أو أحدث
- pip (مدير حزم Python)

### خطوات التثبيت

1. **استنساخ المشروع أو تحميل الملفات**
   ```bash
   # إذا كان لديك git
   git clone <repository-url>
   cd movie-scraper
   ```

2. **إنشاء بيئة افتراضية (اختياري ولكن مُوصى به)**
   ```bash
   python -m venv venv
   
   # على Windows
   venv\Scripts\activate
   
   # على macOS/Linux
   source venv/bin/activate
   ```

3. **تثبيت المكتبات المطلوبة**
   ```bash
   pip install -r requirements.txt
   ```

4. **تشغيل التطبيق**
   ```bash
   python app.py
   ```

5. **فتح المتصفح**
   - انتقل إلى: `http://localhost:5000`
   - ستظهر لك واجهة التطبيق

## طريقة الاستخدام 📖

1. **إدخال الرابط**: ضع رابط الصفحة التي تحتوي على قائمة الأفلام أو المسلسلات
2. **استخراج البيانات**: اضغط على زر "استخراج البيانات"
3. **عرض النتائج**: ستظهر النتائج في شكل بطاقات منظمة
4. **تصدير JSON**: اضغط على زر "تصدير JSON" لحفظ البيانات

## أمثلة على الروابط المدعومة 🔗

```
https://w12.my-cima.net/categories-4cima.php?cat=english-movies-4Cima-7&order=DESC&page=2
https://w12.my-cima.net/categories-4cima.php?cat=arabic-movies-4Cima-6
https://w12.my-cima.net/categories-4cima.php?cat=series-4Cima-2
```

## تنسيق البيانات المُصدرة 📄

### للأفلام:
```json
{
  "movies_info": [
    {
      "movies_name": "اسم الفيلم",
      "movies_img": "رابط صورة الفيلم",
      "movies_href": "رابط صفحة الفيلم"
    }
  ]
}
```

### للمسلسلات:
```json
{
  "series_info": [
    {
      "series_name": "اسم المسلسل",
      "series_img": "رابط صورة المسلسل",
      "series_href": "رابط صفحة المسلسل"
    }
  ]
}
```

## الملفات المهمة 📁

- `app.py` - الخادم الرئيسي (Flask Backend)
- `index.html` - واجهة المستخدم
- `script.js` - منطق الواجهة الأمامية
- `requirements.txt` - قائمة المكتبات المطلوبة

## استكشاف الأخطاء 🔧

### مشاكل شائعة وحلولها:

1. **خطأ في الاتصال بالموقع**
   - تأكد من صحة الرابط
   - تحقق من اتصال الإنترنت
   - قد يكون الموقع محجوب أو غير متاح مؤقتاً

2. **لم يتم العثور على بيانات**
   - تأكد من أن الصفحة تحتوي على قائمة أفلام أو مسلسلات
   - قد يكون تصميم الموقع قد تغير

3. **خطأ في تثبيت المكتبات**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt --force-reinstall
   ```

## التطوير والتخصيص 🛠️

يمكنك تخصيص التطبيق عبر:

1. **إضافة مواقع جديدة**: عدّل دالة `scrape_mycima` في `app.py`
2. **تغيير التصميم**: عدّل ملف `index.html` و CSS
3. **إضافة مميزات جديدة**: عدّل `script.js` للواجهة الأمامية

## ملاحظات مهمة ⚠️

- استخدم التطبيق بمسؤولية واحترم شروط استخدام المواقع
- قد تحتاج لاستخدام VPN إذا كان الموقع محجوباً في منطقتك
- التطبيق مُصمم للاستخدام الشخصي والتعليمي

## الدعم والمساعدة 💬

إذا واجهت أي مشاكل أو لديك اقتراحات، يمكنك:
- فتح issue في المشروع
- التواصل مع المطور

---

**تم تطوير هذا التطبيق بواسطة Augment Agent** 🤖
