let scrapedData = null;

document.addEventListener('DOMContentLoaded', function() {
    const urlInput = document.getElementById('url-input');
    const scrapeBtn = document.getElementById('scrape-btn');
    const exportBtn = document.getElementById('export-btn');
    const exportBtn2 = document.getElementById('export-btn-2');
    const loading = document.getElementById('loading');
    const error = document.getElementById('error');
    const results = document.getElementById('results');
    const resultsCount = document.getElementById('results-count');
    const moviesGrid = document.getElementById('movies-grid');

    // Event listeners
    scrapeBtn.addEventListener('click', handleScrape);
    exportBtn.addEventListener('click', exportToJSON);
    exportBtn2.addEventListener('click', exportToJSON);

    async function handleScrape() {
        const url = urlInput.value.trim();
        
        if (!url) {
            showError('يرجى إدخال رابط صحيح');
            return;
        }

        if (!isValidURL(url)) {
            showError('يرجى إدخال رابط صحيح');
            return;
        }

        showLoading(true);
        hideError();
        hideResults();

        try {
            const response = await fetch('/scrape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: url })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'حدث خطأ أثناء استخراج البيانات');
            }

            scrapedData = data;
            displayResults(data);
            
        } catch (err) {
            console.error('Error:', err);
            showError(err.message || 'حدث خطأ أثناء الاتصال بالخادم');
        } finally {
            showLoading(false);
        }
    }

    function displayResults(data) {
        const items = data.movies_info || data.series_info || [];
        const type = data.movies_info ? 'أفلام' : 'مسلسلات';
        
        resultsCount.textContent = `تم العثور على ${items.length} ${type}`;
        
        moviesGrid.innerHTML = '';
        
        items.forEach(item => {
            const card = createMovieCard(item);
            moviesGrid.appendChild(card);
        });

        results.style.display = 'block';
        exportBtn.disabled = false;
    }

    function createMovieCard(item) {
        const card = document.createElement('div');
        card.className = 'movie-card';
        
        const name = item.movies_name || item.series_name || 'غير محدد';
        const img = item.movies_img || item.series_img || 'https://via.placeholder.com/300x200?text=No+Image';
        const href = item.movies_href || item.series_href || '#';
        
        card.innerHTML = `
            <img src="${img}" alt="${name}" class="movie-image" onerror="this.src='https://via.placeholder.com/300x200?text=No+Image'">
            <div class="movie-info">
                <div class="movie-title">${name}</div>
                <a href="${href}" target="_blank" class="movie-link">
                    <i class="fas fa-external-link-alt"></i> عرض الصفحة
                </a>
            </div>
        `;
        
        return card;
    }

    function exportToJSON() {
        if (!scrapedData) {
            showError('لا توجد بيانات للتصدير');
            return;
        }

        const jsonString = JSON.stringify(scrapedData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `movies_data_${new Date().getTime()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    function showLoading(show) {
        loading.style.display = show ? 'block' : 'none';
        scrapeBtn.disabled = show;
    }

    function showError(message) {
        error.textContent = message;
        error.style.display = 'block';
    }

    function hideError() {
        error.style.display = 'none';
    }

    function hideResults() {
        results.style.display = 'none';
        exportBtn.disabled = true;
    }

    function isValidURL(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }
});

// Handle form submission with Enter key
document.getElementById('url-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        document.getElementById('scrape-btn').click();
    }
});
