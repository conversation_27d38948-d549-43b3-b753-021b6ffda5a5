@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    مستخرج بيانات الأفلام والمسلسلات
echo ========================================
echo.

echo جاري التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

echo جاري تثبيت المكتبات المطلوبة...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات
    echo جرب تشغيل الأمر التالي يدوياً:
    echo pip install -r requirements.txt
    pause
    exit /b 1
)

echo ✅ تم تثبيت المكتبات بنجاح
echo.

echo جاري تشغيل التطبيق...
echo 🌐 سيتم فتح التطبيق على: http://localhost:5000
echo 📱 افتح المتصفح وانتقل إلى الرابط أعلاه
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

python app.py

pause
